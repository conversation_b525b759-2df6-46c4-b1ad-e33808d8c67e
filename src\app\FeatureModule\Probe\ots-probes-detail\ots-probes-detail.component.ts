import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { BACK_BTN_TEXT, Cancel, CancelBtn, DateTimeDisplayFormat, DeleteProbeConfirmationConfirmBtn, DeleteProbeConfirmationHeader, DeleteProbeConfirmationMessage, FeatureHistoryDetailHeader, ITEMS_PER_PAGE, PO_NO, PROBE_ALREADY_EDIT_DISABLE, PROBE_ALREADY_EDIT_ENABLE, PROBE_ALREADY_LOCKED, PROBE_ALREADY_UNLOCKED, PROBE_DELETE, PROBE_DETAIL_COUNTRY, PROBE_DETAIL_CREATE_DATE_AND_TIME, PROBE_DETAIL_CUSTOMER_EMAIL, PROBE_DETAIL_CUSTOMER_NAME, PROBE_DETAIL_EDITABLE, PROBE_DETAIL_LOCKED, PROBE_DETAIL_MODIFY_DATE_AND_TIME, PROBE_DETAIL_SALES_ORDER_NUMBER, PROBE_DETAIL_SERIAL_NO, PROBE_DETAIL_STATUS, PROBE_DETAIL_TYPE, PROBE_LAST_CONNECTED_DATE_AND_TIME, PROBE_ORDER_RECORD_TYPE, PROBE_PART_NUMBER, PROBE_SERIAL_NUMBER_NOT_EMPTY, PROBE_STATUS_ENABLE, PROBE_VERSION, ProbDetailResource, SALES_ORDER_PARTIALLY_CONFIGURED, SalesOrderAssociationHeader, Submit, TRANSFER_ORDER, Update_Probe_CancelButton, Update_Probe_OkButton, Update_Probe_title } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { BasicModelConfig } from '../../../model/common/BasicModelConfig.model';
import { BooleanKeyValueMapping } from '../../../model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from '../../../model/common/EnumMapping.model';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { CustomerAssociationModelRequest } from '../../../model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from '../../../model/customer-association-request';
import { TransferProductDetails } from '../../../model/device/TransferProductDetails.model';
import { ConfigureLicenceDetails } from '../../../model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from '../../../model/probe/ConfigureLicenceResponse.model';
import { DeviceListByProbeIdPagableResponse } from '../../../model/probe/DeviceListByProbeIdPagableResponse.model';
import { ProbeConnectionHistoryRequest } from '../../../model/probe/ProbeConnectionHistoryRequest.model';
import { ProbeDetailWithConfig } from '../../../model/probe/ProbeDetailWithConfig.model';
import { ProbeHistoryPagableResponse } from '../../../model/probe/ProbeHistoryPagableResponse.model';
import { ProbeHistoryResponse } from '../../../model/probe/ProbeHistoryResponse.model';
import { IProbeDeviceList } from '../../../model/probe/probeDeviceList.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { ProbeService } from '../../../shared/Service/ProbeService/probe.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from '../../../shared/enum/Operations/ProbeOperations.enum';
import { OSTypeEnum } from '../../../shared/enum/Probe/OSTypeEnum.enum';
import { UpdateProbeTypeService } from '../../../shared/modalservice/Probe/update-probe-type.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { FeatureHistoryDetailService } from '../../../shared/modalservice/feature-history-detail.service';
import { UpdateFeaturesService } from '../../../shared/modalservice/update-features.service';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { UpdateAssociationService } from '../../../shared/update-association.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { ValidationService } from '../../../shared/util/validation.service';
import { ProductConfigStatus } from '../../../shared/enum/SalesOrder/ProductConfigStatus.enum';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';

@Component({
  selector: 'app-ots-probes-detail',
  templateUrl: './ots-probes-detail.component.html',
  styleUrls: ['./ots-probes-detail.component.css']
})
export class OtsProbesDetailComponent implements OnInit {
  @Input("probeId") probeId: any;
  @Input("resource") resource: string;
  @Output("showProbe") showProbe = new EventEmitter;

  loading: boolean = false;
  deviceList: IProbeDeviceList[] = [];

  probeDetailWithConfig: ProbeDetailWithConfig = null;
  dateDisplayFormat: string = DateTimeDisplayFormat;
  totalItems: any;
  totalDevices: number = 0;
  totalDeviceDisplay: number = 0;
  itemsPerPage: any;
  page: number = 0;
  predicate: any;
  previousPage: any;
  reverse: any;
  drpselectsize: number = ITEMS_PER_PAGE;
  historyDrpSelectSize: number = ITEMS_PER_PAGE;
  probeOperations: string[] = [];

  // Pagination - history list
  historyItemsPerPage: number;
  totalHistoryItems: number;
  historyPage: number = 0;
  totalHistories: number = 0;
  totalHistoryDisplay: number = 0;
  historyPreviousPage: any;

  historyList: Array<ProbeHistoryResponse> = [];
  probeDetailForTransferProduct: TransferProductDetails = null;

  // show entry selection
  dataSizes: string[] = [];

  //dateTimeDisplayFormat
  dateTimeDisplayFormat = DateTimeDisplayFormat;

  // Detail Constants
  serialNo: string = PROBE_DETAIL_SERIAL_NO;
  salesOrderNumber: string = PROBE_DETAIL_SALES_ORDER_NUMBER;
  probeType: string = PROBE_DETAIL_TYPE;
  country: string = PROBE_DETAIL_COUNTRY;
  customerName: string = PROBE_DETAIL_CUSTOMER_NAME;
  customerEmail: string = PROBE_DETAIL_CUSTOMER_EMAIL;
  locked: string = PROBE_DETAIL_LOCKED;
  editable: string = PROBE_DETAIL_EDITABLE;
  status: string = PROBE_DETAIL_STATUS;
  createDateAndTime: string = PROBE_DETAIL_CREATE_DATE_AND_TIME;
  modifyDateAndTime: string = PROBE_DETAIL_MODIFY_DATE_AND_TIME;
  backBtnText: string = BACK_BTN_TEXT;
  partNumber: string = PROBE_PART_NUMBER;
  lastConnectedDateAndTime: string = PROBE_LAST_CONNECTED_DATE_AND_TIME;
  probeVersion: string = PROBE_VERSION;
  poNumber: string = PO_NO;
  orderRecordType: string = PROBE_ORDER_RECORD_TYPE;

  //subject
  subscriptionForCommonloading: Subscription;
  subscriptionForisloading: Subscription;
  subscriptionForDownloadZipFileProbSubject: Subscription;
  subscriptionForProbeDetailLoading: Subscription;

  productStatusList: Array<EnumMapping> = [];
  osTypeList: Array<EnumMapping> = [];
  lockUnlockState: Array<BooleanKeyValueMapping> = [];
  editEnableDisableState: Array<BooleanKeyValueMapping> = [];

  //Page Dispaly
  probeDetailDisplay: boolean = false;
  transferOrderSelectionDisaplay: boolean = false;


  constructor(
    private exceptionService: ExceptionHandlingService,
    private updateFeaturesService: UpdateFeaturesService,
    private updateAssociationService: UpdateAssociationService,
    private toste: ToastrService,
    private commonsService: CommonsService,
    private featureHistoryDetailService: FeatureHistoryDetailService,
    private downloadService: DownloadService,
    private customerAssociationService: CustomerAssociationService,
    private commonOperationsService: CommonOperationsService,
    private cdr: ChangeDetectorRef,
    private probeApiService: ProbeApiService,
    private updateProbeTypeService: UpdateProbeTypeService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private confirmDialogService: ConfirmDialogService,
    private validationService: ValidationService,
    private enumMappingDisplayNamePipe: EnumMappingDisplayNamePipe,
    private moduleValidationServiceService: ModuleValidationServiceService,
    private probeService: ProbeService,
    private probeOperationService: ProbeOperationService) {
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.historyItemsPerPage = ITEMS_PER_PAGE;
  }

  ngOnInit() {
    this.probeDetailDisplay = true;
    //Get enabled / Disabled Option list
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
    this.osTypeList = this.keyValueMappingServiceService.enumOptionToList(OSTypeEnum);
    //Get Locked/Unlocked Option list
    this.lockUnlockState = this.keyValueMappingServiceService.lockedUnlockOptionList();
    this.editEnableDisableState = this.keyValueMappingServiceService.editEnableDisableOptionList();
    this.getProbeDetailInfo(this.probeId);
    this.getLicenceHistoryListOfProbeId(this.probeId);
    this.getDeviceListOfProbeId(this.probeId);
    this.dataSizes = this.commonsService.accessDataSizes();
    this.subjectInit();
  }

  /**
   * Loading subject to loading start and stop.
   * Download subject -> Open conirmation model after subscribe subject and call download api.
   */
  private subjectInit(): void {
    // Probe detail loading subscription (following device-detail pattern)
    this.subscriptionForProbeDetailLoading = this.probeOperationService.getProbeDetailLoadingSubject()?.subscribe((status: boolean) => {
      this.setLoadingStatus(status);
    });
    this.subscriptionForisloading = this.downloadService.getisLoadingSubjectForProbDetailPage()?.subscribe((res: boolean) => {
      this.loading = res;
    });
    this.subscriptionForCommonloading = this.commonOperationsService.getCommonLoadingSubject()?.subscribe((res: boolean) => {
      this.cdr.detectChanges();
      this.loading = res;
      this.cdr.detectChanges();
    });
    this.subscriptionForDownloadZipFileProbSubject = this.downloadService.getdownloadZipFileForProbDetailPageSubject()?.subscribe((res: boolean) => {
      if (res) {
        this.commonOperationsService.changeOperationForProbe(ProbeOperationsEnum.DOWNLOAD_PROBES, ProbDetailResource, [this.probeId], [this.probeDetailWithConfig]);
      }
    });
  }

  /**
  * Loading Status
  * <AUTHOR>
  */
  private setLoadingStatus(status: boolean): void {
    this.cdr.detectChanges();
    this.loading = status;
    this.cdr.detectChanges();
  }

  ngOnDestroy() {
    if (this.subscriptionForisloading != undefined) {
      this.subscriptionForisloading.unsubscribe();
    }
    if (this.subscriptionForDownloadZipFileProbSubject != undefined) {
      this.subscriptionForDownloadZipFileProbSubject.unsubscribe();
    }
    if (this.subscriptionForCommonloading != undefined) {
      this.subscriptionForCommonloading.unsubscribe();
    }
    if (this.subscriptionForProbeDetailLoading != undefined) {
      this.subscriptionForProbeDetailLoading.unsubscribe();
    }
  }

  private getProbeDetailInfo(probeId: number): void {
    this.loading = true;
    this.probeApiService.getProbeDetailInfo(probeId)?.subscribe({
      next: (res: HttpResponse<ProbeDetailWithConfig>) => {
        if (res.status == 200) {
          this.probeDetailWithConfig = res.body;
          this.loading = false;
          this.probeDetailForTransferProduct = new TransferProductDetails(this.probeId, this.probeDetailWithConfig?.salesOrderId, this.probeDetailWithConfig.serialNumber, this.probeDetailWithConfig.type, ProbDetailResource);
          let transferProduct: boolean = this.probeDetailWithConfig.orderRecordType === TRANSFER_ORDER;
          this.probeOperations = this.commonOperationsService.accessProbeOperations(false, true, transferProduct, this.resource);
        } else {
          this.loading = false;
          this.toste.info(PROBE_DELETE);
          this.back();
        }
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
   * View list of Licence history with pagination
   * @param probeId 
   */
  private getLicenceHistoryListOfProbeId(probeId: number): void {
    this.loading = true;
    this.probeApiService.getProbeHistory(probeId, {
      page: this.historyPage - 1,
      size: this.historyItemsPerPage
    })?.subscribe(
      {
        next: (res: HttpResponse<ProbeHistoryPagableResponse>) => {
          if (res.body) {
            this.historyList = res.body.content;
            this.totalHistoryItems = parseInt(res.body.totalElements.toString(), 10);
            this.historyPage = res.body.number + 1;
            this.totalHistories = res.body.totalElements;
            this.totalHistoryDisplay = res.body.numberOfElements;
            this.loading = false;
          }
        },
        error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  private getDeviceListOfProbeId(probeId: number): void {
    this.loading = true;
    this.probeApiService.getDeviceListByProbeId(probeId, {
      page: this.page - 1,
      size: this.itemsPerPage
    })?.subscribe({
      next: (res: HttpResponse<DeviceListByProbeIdPagableResponse>) => {
        if (res.body) {
          this.deviceList = res.body.content;
          this.totalItems = parseInt(res.body.totalElements.toString(), 10);
          this.page = res.body.number + 1;
          this.totalDevices = res.body.totalElements;
          this.totalDeviceDisplay = res.body.numberOfElements;
          this.loading = false;
        }
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  back() {
    this.showProbe.emit();
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.getDeviceListOfProbeId(this.probeId);
    }
  }

  /**
   * load history pagination
   * @param historyPage 
   */
  public loadFeatureHistoryPage(historyPage: number): void {
    if (historyPage !== this.historyPreviousPage) {
      this.historyPreviousPage = historyPage;
      this.getLicenceHistoryListOfProbeId(this.probeId);
    }
  }

  public changeDataSize(event: any): void {
    this.loading = true;
    this.itemsPerPage = event.target.value;
    this.getDeviceListOfProbeId(this.probeId);
  }

  /**
   * change history items size
   * @param event 
   */
  public changeFeatureHistoryDataSize(event: any): void {
    this.loading = true;
    this.historyItemsPerPage = event.target.value;
    this.getLicenceHistoryListOfProbeId(this.probeId);
  }

  /**
   * probe operations
   * Uses centralized operation handler from common operations service for all operations
   * <AUTHOR>
   * @param event
   */
  public changeProbeOperation(event: any): void {
    const operationName = event.target.value;

    // All operations are now handled by common operations service
    this.commonOperationsService.changeOperationForProbe(operationName, ProbDetailResource, [this.probeId], [this.probeDetailWithConfig]);

    let selection = document.getElementById('probeOperation') as HTMLSelectElement;
    selection.value = ProbeOperationsEnum.Probe_Operations;
  }

  /**
   * View feature history details in popup
   * @param history 
   */
  public openProbeConnectionHistory(history: ProbeHistoryResponse): void {
    this.featureHistoryDetailService.openFeatureHistoryDetailModel(
      FeatureHistoryDetailHeader, history, this.probeId
    ).then(response => { }, () => { });
  }

  /**
* transfer Probe
* 
* <AUTHOR>
*/
  public transferProbe(): void {
    if (this.validateWithUserInfo()) {
      if (this.probeDetailWithConfig.serialNumber === null) {
        this.toste.info(PROBE_SERIAL_NUMBER_NOT_EMPTY);
      }
      else if (ProductStatusEnum[this.probeDetailWithConfig.productStatus] !== ProductStatusEnum.ENABLED) {
        this.toste.info(PROBE_STATUS_ENABLE);
      }
      else if (ProductConfigStatus[this.probeDetailWithConfig.soStatus] === ProductConfigStatus.PARTIALLY_CONFIGURED) {
        this.toste.info(SALES_ORDER_PARTIALLY_CONFIGURED);
      } else {
        this.transferOrderSelectionToggle(false, true);
      }
    }
  }

  /**
  * Toggles the display states for probe details and transfer order sections.
  * 
  * <AUTHOR>
  * 
  * @param probeDetailDisplay 
  * @param tranferOrderSectionDisplay
  */
  public transferOrderSelectionToggle(probeDetailDisplay: boolean, tranferOrderSectionDisplay: boolean) {
    this.transferOrderSelectionDisaplay = tranferOrderSectionDisplay;
    this.probeDetailDisplay = probeDetailDisplay;
    if (probeDetailDisplay) {
      this.getProbeDetailInfo(this.probeId);
    }
  }

  /**
  * Validation With Probe Edit Enable/Disable Status
  * 
  * <AUTHOR>
  * @returns 
  */
  private validateWithUserInfo(): boolean {
    let moduleLockedState = this.moduleValidationServiceService.validateWithEditStateForSingleRecord(this.probeDetailWithConfig?.editable, ProbDetailResource);
    return moduleLockedState ? this.moduleValidationServiceService.validateWithUserCountryForSingleRecord(this.probeDetailWithConfig?.country, ProbDetailResource, true) : false;
  }



  /**
  * Refresh Probe Connection History Data
  * 
  * <AUTHOR> 
  */
  public refreshConnectionHistory(): void {
    this.getDeviceListOfProbeId(this.probeId);
  }

  /**
  * Refresh Probe License History Data
  * 
  * <AUTHOR> 
  */
  public refreshLicenseHistory(): void {
    this.getLicenceHistoryListOfProbeId(this.probeId);
  }
  /**
  * Refresh Probe Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshProbeDetailPage(): void {
    this.getProbeDetailInfo(this.probeId);
    this.getLicenceHistoryListOfProbeId(this.probeId);
    this.getDeviceListOfProbeId(this.probeId);
  }
}
